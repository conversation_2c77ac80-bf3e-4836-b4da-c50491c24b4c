import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ChangeDetectorRef, NgZone } from '@angular/core';
import { TickerItemComponent } from './ticker-item.component';
import { LogService } from '../../../core/services/log.service';
import { PlaylistItem } from '../../../core/models/playlist.model';

describe('TickerItemComponent', () => {
  let component: TickerItemComponent;
  let fixture: ComponentFixture<TickerItemComponent>;
  let mockLogService: jasmine.SpyObj<LogService>;
  let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;
  let mockNgZone: jasmine.SpyObj<NgZone>;

  beforeEach(async () => {
    mockLogService = jasmine.createSpyObj('LogService', ['info', 'warn', 'error']);
    mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']);
    mockNgZone = jasmine.createSpyObj('NgZone', ['run']);
    
    // Mock <PERSON>one.run to execute the callback immediately
    mockNgZone.run.and.callFake((fn: Function) => fn());

    await TestBed.configureTestingModule({
      imports: [TickerItemComponent],
      providers: [
        { provide: LogService, useValue: mockLogService },
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
        { provide: NgZone, useValue: mockNgZone }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TickerItemComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with loading state', () => {
    expect(component.loading).toBe(true);
    expect(component.isAnimating).toBe(false);
  });

  it('should handle null item gracefully', fakeAsync(() => {
    spyOn(component.loaded, 'emit');
    
    component.item = null;
    component.ngOnInit();
    tick(200);

    expect(component.loading).toBe(false);
    expect(component.loaded.emit).toHaveBeenCalled();
  }));

  it('should initialize ticker text from item content URL', fakeAsync(() => {
    const mockItem: PlaylistItem = {
      id: '1',
      name: 'Test Ticker',
      type: 'ticker',
      content: { url: 'Breaking News: Test ticker message' },
      duration: 10,
      order: 1
    };

    spyOn(component.loaded, 'emit');
    
    component.item = mockItem;
    component.preload = false;
    component.ngOnInit();
    tick(200);

    expect(component.tickerText).toBe('Breaking News: Test ticker message');
    expect(component.loading).toBe(false);
    expect(component.loaded.emit).toHaveBeenCalled();
    expect(mockLogService.info).toHaveBeenCalledWith(jasmine.stringMatching(/Ticker item initialized/));
  }));

  it('should start animation when not in preload mode', fakeAsync(() => {
    const mockItem: PlaylistItem = {
      id: '1',
      name: 'Test Ticker',
      type: 'ticker',
      content: { url: 'Test message' },
      duration: 5,
      order: 1
    };

    component.item = mockItem;
    component.preload = false;
    component.ngOnInit();
    tick(500); // Wait for animation start delay

    expect(component.isAnimating).toBe(true);
    expect(mockNgZone.run).toHaveBeenCalled();
    expect(mockChangeDetectorRef.detectChanges).toHaveBeenCalled();
  }));

  it('should not start animation when in preload mode', fakeAsync(() => {
    const mockItem: PlaylistItem = {
      id: '1',
      name: 'Test Ticker',
      type: 'ticker',
      content: { url: 'Test message' },
      duration: 5,
      order: 1
    };

    component.item = mockItem;
    component.preload = true;
    component.ngOnInit();
    tick(500);

    expect(component.isAnimating).toBe(false);
  }));

  it('should emit ended event after duration completes', fakeAsync(() => {
    const mockItem: PlaylistItem = {
      id: '1',
      name: 'Test Ticker',
      type: 'ticker',
      content: { url: 'Test message' },
      duration: 2, // 2 seconds
      order: 1
    };

    spyOn(component.ended, 'emit');
    
    component.item = mockItem;
    component.preload = false;
    component.ngOnInit();
    
    // Fast forward 2 seconds
    tick(2000);

    expect(component.ended.emit).toHaveBeenCalled();
    expect(mockLogService.info).toHaveBeenCalledWith('Ticker duration completed, emitting ended event');
  }));

  it('should stop animation when stopAnimation is called', () => {
    component.isAnimating = true;
    component.stopAnimation();

    expect(component.isAnimating).toBe(false);
    expect(mockNgZone.run).toHaveBeenCalled();
    expect(mockChangeDetectorRef.detectChanges).toHaveBeenCalled();
    expect(mockLogService.info).toHaveBeenCalledWith('Stopping ticker animation');
  });

  it('should start animation when startAnimation is called', fakeAsync(() => {
    component.startAnimation();
    tick(200); // Wait for animation start delay

    expect(component.isAnimating).toBe(true);
    expect(mockNgZone.run).toHaveBeenCalled();
    expect(mockChangeDetectorRef.detectChanges).toHaveBeenCalled();
    expect(mockLogService.info).toHaveBeenCalledWith('Starting ticker animation');
  }));

  it('should clean up timers on destroy', () => {
    component.ngOnDestroy();
    // Should not throw any errors
    expect(component).toBeTruthy();
  });

  it('should return proper ticker styles', () => {
    const styles = component.getTickerStyle();
    
    expect(styles).toEqual({
      color: 'white',
      fontSize: '2rem',
      textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'
    });
  });
});
