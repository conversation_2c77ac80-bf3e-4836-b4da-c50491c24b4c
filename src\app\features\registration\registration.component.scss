:host {
  display: block;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1a237e, #283593);
  font-family: '<PERSON>o', sans-serif;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.registration-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  padding: 2rem;
  position: relative;
  box-sizing: border-box;
}

.registration-card {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  padding: 2.5rem;
  width: 90%;
  max-width: 600px;
  text-align: center;
  position: relative;
  z-index: 10;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo {
  margin-bottom: 2rem;
  padding-top: 0.5rem;
}

.logo img {
  max-width: 200px;
}

.logo div {
  font-size: 2.25rem;
  font-weight: 700;
  color: #3f51b5;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 1.75rem;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  font-weight: 600;
  line-height: 1.3;
}

.content {
  margin-top: 1rem;
  padding: 0 1rem;
}

.content p {
  margin-bottom: 1rem;
  color: #555;
  line-height: 1.5;
}

.code-display {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 2.5rem 0;
  gap: 0.75rem;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.code-display span {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.5rem;
  height: 4.5rem;
  background: linear-gradient(145deg, #ffffff, #f0f4ff);
  border-radius: 12px;
  font-size: 2.25rem;
  font-weight: 700;
  color: #2c3e50;
  box-shadow: 
    0 4px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  font-family: 'Courier New', monospace;
  letter-spacing: 0.05em;
}

.code-display span::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 12px;
  pointer-events: none;
}

/* Enhanced hover animation */
.code-display span:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 
    0 8px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  background: linear-gradient(145deg, #f8faff, #e8f2ff);
}

/* Pulse animation for attention */
@keyframes pulse {
  0%, 100% { 
    box-shadow: 
      0 4px 8px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05),
      0 0 0 0 rgba(102, 126, 234, 0.4);
  }
  50% { 
    box-shadow: 
      0 4px 8px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05),
      0 0 0 8px rgba(102, 126, 234, 0.1);
  }
}

.code-display span {
  animation: pulse 2s infinite;
}

.code-display span:nth-child(1) { animation-delay: 0s; }
.code-display span:nth-child(2) { animation-delay: 0.1s; }
.code-display span:nth-child(3) { animation-delay: 0.2s; }
.code-display span:nth-child(4) { animation-delay: 0.3s; }
.code-display span:nth-child(5) { animation-delay: 0.4s; }
.code-display span:nth-child(6) { animation-delay: 0.5s; }

.status-message {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

.help-text {
  margin-top: 2rem;
  color: #757575;
  font-size: 0.9rem;
}

.loader {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3f51b5;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error {
  color: #d32f2f;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-icon .material-icons {
  font-size: 3rem;
  color: #d32f2f;
}

.success-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.success-icon .material-icons {
  font-size: 3rem;
  color: #4caf50;
}

.error-message {
  margin-bottom: 1.5rem;
}

.retry-button {
  background-color: #3f51b5;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #303f9f;
}

/* Saved indicator styles */
.saved-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: #e8f5e9;
  border-radius: 4px;
  color: #2e7d32;
}

.saved-indicator .material-icons {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.saved-indicator p {
  font-size: 0.9rem;
  margin: 0;
}

/* Regenerate button styles */
.regenerate-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.5rem auto 0;
  padding: 0.5rem 1rem;
  background-color: transparent;
  color: #3f51b5;
  border: 1px solid #3f51b5;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.regenerate-button .material-icons {
  font-size: 1rem;
  margin-right: 0.5rem;
}

.regenerate-button:hover {
  background-color: #f5f8ff;
}

.device-info {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 8px;
  color: white;
  font-size: 0.75rem;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.device-info p {
  margin: 0.5rem 0;
}