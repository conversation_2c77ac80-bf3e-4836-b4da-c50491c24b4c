<main>
  <!-- Sync Overlay -->
  <div *ngIf="showSyncOverlay" class="sync-overlay">
    <div class="sync-content" *ngIf="syncStatus$ | async as status">
      <div class="sync-header">
        <h2>Synchronizing Content</h2>
        <p>Downloading playlists and content for offline use...</p>
      </div>
      
      <div class="sync-progress">
        <div class="progress-bar">
          <div class="progress-fill" [style.width.%]="status.progress"></div>
        </div>
        <span class="progress-text">{{status.progress}}%</span>
      </div>
      
      <div class="sync-details">
        <p class="sync-step">{{status.currentStep}}</p>
        <div class="sync-stats" *ngIf="status.totalPlaylists > 0">
          <span>Playlists: {{status.downloadedPlaylists}}/{{status.totalPlaylists}}</span>
          <span *ngIf="status.totalContent > 0">Content: {{status.downloadedContent}}/{{status.totalContent}}</span>
        </div>
        <p *ngIf="status.error" class="sync-error">{{status.error}}</p>
      </div>
    </div>
  </div>

  <!-- Main App Content -->
  <router-outlet></router-outlet>
  
  <!-- Orientation Indicator -->
  <div class="orientation-indicator" *ngIf="orientationLabel">
    {{ orientationLabel }}
  </div>

  <!-- Offline Indicator -->
  <div class="offline-indicator" *ngIf="!isOnline">
    <span>Offline Mode</span>
  </div>
</main>
