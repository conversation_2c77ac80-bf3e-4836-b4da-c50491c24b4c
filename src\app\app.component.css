*, *::before, *::after {
  box-sizing: border-box;
}

:host {
  display: block;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  background-color: #000;
  margin: 0 !important;
  padding: 0 !important;
}

main {
  width: 100vw;
  height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
}

/* Sync Overlay Styles */
.sync-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sync-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.sync-header h2 {
  font-size: 2.5rem;
  margin: 0 0 10px 0;
  font-weight: 300;
}

.sync-header p {
  font-size: 1.1rem;
  margin: 0 0 30px 0;
  opacity: 0.9;
}

.sync-progress {
  margin: 30px 0;
  display: flex;
  align-items: center;
  gap: 15px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ade80, #22c55e);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 1.2rem;
  font-weight: 600;
  min-width: 50px;
}

.sync-details {
  margin-top: 30px;
}

.sync-step {
  font-size: 1rem;
  margin: 0 0 15px 0;
  opacity: 0.9;
}

.sync-stats {
  display: flex;
  justify-content: center;
  gap: 20px;
  font-size: 0.9rem;
  opacity: 0.8;
}

.sync-error {
  color: #ff6b6b;
  font-size: 0.9rem;
  margin: 15px 0 0 0;
  padding: 10px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

/* Offline Indicator */
.offline-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(255, 152, 0, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.offline-indicator span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.offline-indicator span::before {
  content: '●';
  color: #ff9800;
  animation: pulse 2s infinite;
}

.orientation-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0,0,0,0.6);
  color: white;
  padding: 10px 20px;
  border-radius: 10px;
  font-size: 1.5rem;
  z-index: 10001;
  pointer-events: none;
  animation: fadeOut 2s forwards;
}

@keyframes fadeOut {
  0% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Portrait mode is handled globally in styles.css */