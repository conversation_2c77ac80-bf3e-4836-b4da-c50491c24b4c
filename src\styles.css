/* Global reset for perfect fullscreen fit */
* {
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  border: none !important;
  outline: none !important;
  max-width: none !important;
  max-height: none !important;
}

html {
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  -webkit-overflow-scrolling: touch !important;
  background: #000 !important;
}

body {
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  background-color: #000 !important;
  font-family: system-ui, -apple-system, sans-serif !important;
  touch-action: none !important;
}

/* Ensure router outlet fills the screen perfectly */
router-outlet {
  display: block;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
}

/* Remove all default spacing from common elements */
div, main, section, article, header, footer, nav, aside {
  margin: 0 !important;
  padding: 0 !important;
}

/* Portrait orientation adjustments */
@media (orientation: portrait) {
  /* Ensure .fullscreen-image respects portrait dimensions */
  .fullscreen-image {
    width: 100vh !important;
    height: 100vw !important;
    min-width: 100vh !important;
    min-height: 100vw !important;
    object-fit: fill !important;
  }
  /* Ensure media fills screen after rotation */
  .player-container img,
  .player-container video,
  .player-container iframe,
  .content-container,
  .content-item {
    width: 100vh !important;
    height: 100vw !important;
    object-fit: fill !important;
  }
  .player-container {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100vh !important;
    height: 100vw !important;
    transform: translate(-50%, -50%) rotate(90deg);
    transform-origin: center center;
  }
}

/* Existing manual class option */
body.portrait-mode .player-container {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100vh !important;
  height: 100vw !important;
  transform: translate(-50%, -50%) rotate(90deg);
  transform-origin: center center;
}

body.portrait-mode .content-container,
body.portrait-mode .content-item {
  width: 100vh !important;
  height: 100vw !important;
}

/* Media fill for manual portrait-mode */
body.portrait-mode .fullscreen-image {
  width: 100vh !important;
  height: 100vw !important;
  min-width: 100vh !important;
  min-height: 100vw !important;
  object-fit: fill !important;
}
body.portrait-mode img,
body.portrait-mode iframe,
body.portrait-mode .fullscreen-image {
  width: 100vh !important;
  height: 100vw !important;
  object-fit: fill !important;
}

body.portrait-mode video,
body.portrait-mode .video-container video {
  width: 100vh !important;
  height: 100vw !important;
  object-fit: fill !important;
}

/* Prevent any scrolling behaviors */
html, body {
  scroll-behavior: auto;
  overscroll-behavior: none;
  -webkit-overscroll-behavior: none;
}