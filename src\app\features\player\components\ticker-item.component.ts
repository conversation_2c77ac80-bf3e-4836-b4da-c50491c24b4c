// ticker-item.component.ts
import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectorRef, NgZone } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PlaylistItem } from '../../../core/models/playlist.model';
import { LogService } from '../../../core/services/log.service';

@Component({
  selector: 'app-ticker-item',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="ticker-container">
      @if (item && !loading) {
        <div class="ticker-content"
             [ngClass]="{ 'animate': isAnimating }"
             [ngStyle]="getTickerStyle()">
          {{ tickerText }}
        </div>
      }

      @if (loading) {
        <div class="loading-indicator">
          <div class="spinner"></div>
        </div>
      }
    </div>
  `,
  styles: [`
    :host {
      display: block;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .ticker-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      overflow: hidden;
    }

    .ticker-content {
      white-space: nowrap;
      font-size: 2rem;
      font-weight: bold;
      transform: translateX(100%);
      transition: none;
    }

    .ticker-content.animate {
      animation: ticker-scroll 20s linear infinite;
    }

    .loading-indicator {
      display: flex;
      align-items: center;
      justify-content: center;

      .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
    }

    @keyframes ticker-scroll {
      from {
        transform: translateX(100%);
      }
      to {
        transform: translateX(-100%);
      }
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  `]
})
export class TickerItemComponent implements OnInit, OnDestroy {
  @Input() item: PlaylistItem | null = null;
  @Input() preload = false;
  @Input() duration: number = 10;
  @Output() ended = new EventEmitter<void>();
  @Output() loaded = new EventEmitter<void>();

  tickerText: string = '';
  loading = true;
  isAnimating = false;
  private timer: any;
  private animationStartTimer: any;

  constructor(
    private logService: LogService,
    private cdr: ChangeDetectorRef,
    private zone: NgZone
  ) {}

  ngOnInit(): void {
    this.loading = true;
    this.isAnimating = false;

    if (!this.item) {
      this.loading = false;
      this.loaded.emit();
      return;
    }

    // For a ticker, the content URL actually contains the text
    this.tickerText = this.item.content.url;
    this.loading = false;

    this.logService.info(`Ticker item initialized: ${this.tickerText.substring(0, 50)}...`);

    // Emit loaded event for carousel coordination
    setTimeout(() => {
      this.loaded.emit();
    }, 100);

    // If not in preload mode, start the animation and timer
    if (!this.preload) {
      this.startAnimation();
      this.startTimer();
    }
  }

  ngOnDestroy(): void {
    this.clearTimer();
    this.clearAnimationTimer();
    this.stopAnimation();
  }

  // Method to start animation when item becomes active
  startAnimation(): void {
    this.logService.info('Starting ticker animation');

    // Small delay to ensure DOM is ready
    this.animationStartTimer = setTimeout(() => {
      this.zone.run(() => {
        this.isAnimating = true;
        this.cdr.detectChanges();
      });
    }, 200);
  }

  // Method to stop animation
  stopAnimation(): void {
    this.logService.info('Stopping ticker animation');
    this.zone.run(() => {
      this.isAnimating = false;
      this.cdr.detectChanges();
    });
  }

  private startTimer(): void {
    // Clear any existing timer
    this.clearTimer();

    // Use the specified duration or default to 10 seconds
    const duration = this.duration || this.item?.duration || 10;

    this.logService.info(`Starting ticker timer for ${duration} seconds`);

    // Set timer for the duration of this item
    this.timer = setTimeout(() => {
      this.logService.info('Ticker duration completed, emitting ended event');
      this.stopAnimation();
      this.ended.emit();
    }, duration * 1000);
  }

  private clearTimer(): void {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  private clearAnimationTimer(): void {
    if (this.animationStartTimer) {
      clearTimeout(this.animationStartTimer);
      this.animationStartTimer = null;
    }
  }

  getTickerStyle(): any {
    // Add any custom styling for the ticker
    return {
      color: 'white',
      fontSize: '2rem',
      textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)'
    };
  }
}