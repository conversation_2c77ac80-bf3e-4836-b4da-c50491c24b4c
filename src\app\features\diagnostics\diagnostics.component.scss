:host {
  display: block;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  background-color: #f5f5f5;
  font-family: 'Roboto', sans-serif;
}

.diagnostics-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e0e0e0;
}

h1 {
  font-size: 1.5rem;
  color: #333;
  margin: 0;
}

h2 {
  font-size: 1.2rem;
  color: #555;
  margin: 0 0 1rem 0;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  background-color: #f0f0f0;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: #e0e0e0;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

section {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.info-card {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 1rem;
}

.label {
  font-size: 0.8rem;
  color: #666;
  margin: 0 0 0.3rem 0;
}

.value {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
  word-break: break-word;
}

.status-online {
  color: #2e7d32;
}

.status-offline {
  color: #c62828;
}

.log-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.log-filter {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 0.5rem;
  font-family: monospace;
}

.log-entry {
  padding: 0.3rem 0.5rem;
  font-size: 0.8rem;
  border-radius: 2px;
  margin-bottom: 2px;
  display: flex;
  align-items: flex-start;
}

.log-debug {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.log-info {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.log-warn {
  background-color: #fff8e1;
  color: #ff6f00;
}

.log-error {
  background-color: #ffebee;
  color: #b71c1c;
}

.log-time {
  flex: 0 0 80px;
}

.log-level {
  flex: 0 0 60px;
  font-weight: bold;
}

.log-message {
  flex: 1;
  word-break: break-word;
}

.no-logs {
  text-align: center;
  padding: 1rem;
  color: #777;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.problem-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: none;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.problem-button:hover {
  background-color: #e0e0e0;
}

.problem-button.danger {
  background-color: #ffebee;
  color: #c62828;
}

.problem-button.danger:hover {
  background-color: #ffcdd2;
}

.footer {
  margin-top: 2rem;
  text-align: center;
  color: #777;
  font-size: 0.8rem;
}

.primary {
  background-color: #3f51b5;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primary:hover {
  background-color: #303f9f;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}