# Dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Build outputs
dist/
build/
out/
.next/
.angular/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS files
.DS_Store
Thumbs.db
desktop.ini

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.project
.classpath
.settings/

# Logs
logs
*.log

# Media files that might be large
*.mp4
*.mov
*.avi
*.wmv
*.flv
*.webm

# Temporary files
.tmp/
.temp/
tmp/
temp/

# Coverage directory
coverage/
.nyc_output/

# Debug files
.debug/

# Local configuration
.local/