import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { LogService } from './log.service';

@Injectable({
  providedIn: 'root'
})
export class WakeLockService {
  private wakeLock: WakeLockSentinel | null = null;
  private isBrowser: boolean;
  private isSupported: boolean = false;
  private keepAliveInterval: any = null;
  private wakeLockRetryInterval: any = null;
  private mouseMoveInterval: any = null;
  private videoElement: HTMLVideoElement | null = null;
  private isActive: boolean = false;
  private retryCount: number = 0;
  private maxRetries: number = 100;

  constructor(
    private logService: LogService,
    @Inject(PLATFORM_ID) private platformId: any
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    this.checkSupport();
  }

  /**
   * Check if Screen Wake Lock API is supported
   */
  private checkSupport(): void {
    if (!this.isBrowser) {
      this.isSupported = false;
      return;
    }

    this.isSupported = 'wakeLock' in navigator && 'request' in (navigator as any).wakeLock;
    
    if (this.isSupported) {
      this.logService.info('Screen Wake Lock API supported');
    } else {
      this.logService.warn('Screen Wake Lock API not supported - using fallback methods');
    }
  }

  /**
   * Check if wake lock is currently supported
   */
  isWakeLockSupported(): boolean {
    return this.isSupported;
  }

  /**
   * Check if any keep-alive method is currently active
   */
  isWakeLockActive(): boolean {
    return this.isActive;
  }

  /**
   * Start all available methods to keep screen awake - NEVER LET IT SLEEP!
   */
  async requestWakeLock(): Promise<boolean> {
    if (!this.isBrowser) {
      this.logService.debug('Wake lock request skipped - not running in browser');
      return false;
    }

    this.logService.info('🔥 ACTIVATING AGGRESSIVE SCREEN WAKE LOCK - NEVER SLEEP MODE! 🔥');
    this.isActive = true;

    // Method 1: Official Wake Lock API (if supported)
    if (this.isSupported) {
      await this.requestOfficialWakeLock();
    }

    // Method 2: Create invisible video element to prevent sleep
    this.createInvisibleVideo();

    // Method 3: Periodic mouse movements to simulate activity
    this.startMouseMovements();

    // Method 4: Periodic keep-alive actions
    this.startKeepAliveActions();

    // Method 5: Continuous wake lock retry mechanism
    this.startWakeLockRetry();

    // Method 6: Prevent screen saver and power management
    this.preventScreenSaver();

    // Method 7: Handle visibility changes aggressively
    this.setupVisibilityHandling();

    this.logService.info('🚀 ALL KEEP-ALIVE METHODS ACTIVATED - SCREEN WILL NEVER SLEEP! 🚀');
    return true;
  }

  /**
   * Method 1: Official Wake Lock API with aggressive retry
   */
  private async requestOfficialWakeLock(): Promise<void> {
    try {
      // Release existing wake lock first
      if (this.wakeLock && !this.wakeLock.released) {
        await this.wakeLock.release();
      }

      this.wakeLock = await (navigator as any).wakeLock.request('screen');
      this.logService.info('✅ Official Wake Lock acquired successfully');
      this.retryCount = 0;

      if (this.wakeLock) {
        this.wakeLock.addEventListener('release', () => {
          this.logService.warn('⚠️ Wake Lock was released - immediately reacquiring!');
          this.wakeLock = null;
          // Immediately try to reacquire
          setTimeout(() => this.requestOfficialWakeLock(), 100);
        });
      }
    } catch (error: any) {
      this.retryCount++;
      this.logService.error(`❌ Failed to acquire wake lock (attempt ${this.retryCount}): ${error.message}`);
      
      if (this.retryCount < this.maxRetries) {
        // Retry after 1 second
        setTimeout(() => this.requestOfficialWakeLock(), 1000);
      }
    }
  }

  /**
   * Method 2: Create invisible video to prevent sleep
   */
  private createInvisibleVideo(): void {
    try {
      // Remove existing video if any
      if (this.videoElement) {
        this.videoElement.remove();
      }

      // Create invisible video element
      this.videoElement = document.createElement('video');
      this.videoElement.style.position = 'fixed';
      this.videoElement.style.top = '-1000px';
      this.videoElement.style.left = '-1000px';
      this.videoElement.style.width = '1px';
      this.videoElement.style.height = '1px';
      this.videoElement.style.opacity = '0';
      this.videoElement.style.pointerEvents = 'none';
      this.videoElement.muted = true;
      this.videoElement.loop = true;
      this.videoElement.autoplay = true;

      // Create a minimal video data URL (1x1 transparent pixel video)
      const videoDataUrl = 'data:video/mp4;base64,AAAAIGZ0eXBpc29tAAACAGlzb21pc28yYXZjMW1wNDEAAAAIZnJlZQAAACtmZGF0AAAAL2F2YzFhdmMxVGFrZUNhcmUh';
      this.videoElement.src = videoDataUrl;

      document.body.appendChild(this.videoElement);

      this.videoElement.play().catch(() => {
        // If autoplay fails, try to play periodically
        setInterval(() => {
          if (this.videoElement && this.videoElement.paused) {
            this.videoElement.play().catch(() => {});
          }
        }, 1000);
      });

      this.logService.info('✅ Invisible video element created to prevent sleep');
    } catch (error) {
      this.logService.error('❌ Failed to create invisible video element');
    }
  }

  /**
   * Method 3: Simulate mouse movements to keep system active
   */
  private startMouseMovements(): void {
    if (this.mouseMoveInterval) {
      clearInterval(this.mouseMoveInterval);
    }

    this.mouseMoveInterval = setInterval(() => {
      try {
        // Create and dispatch mouse move events
        const event = new MouseEvent('mousemove', {
          clientX: Math.random() * 10,
          clientY: Math.random() * 10,
          bubbles: true
        });
        document.dispatchEvent(event);

        // Also dispatch touch events for mobile devices
        const touchEvent = new TouchEvent('touchstart', {
          bubbles: true,
          touches: [] as any
        });
        document.dispatchEvent(touchEvent);
      } catch (error) {
        // Ignore errors - this is a fallback method
      }
    }, 5000); // Every 5 seconds

    this.logService.info('✅ Mouse movement simulation started');
  }

  /**
   * Method 4: Periodic keep-alive actions
   */
  private startKeepAliveActions(): void {
    if (this.keepAliveInterval) {
      clearInterval(this.keepAliveInterval);
    }

    this.keepAliveInterval = setInterval(() => {
      try {
        // Method 4a: Request animation frame to keep GPU active
        requestAnimationFrame(() => {});

        // Method 4b: Create temporary canvas to keep GPU busy
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.fillRect(0, 0, 1, 1);
        }

        // Method 4c: Trigger scroll events
        window.scrollBy(0, 0);

        // Method 4d: Update document title to show activity
        const originalTitle = document.title;
        document.title = `${originalTitle} • ${new Date().getSeconds()}`;
        setTimeout(() => {
          document.title = originalTitle;
        }, 100);

        // Method 4e: Play silent audio to keep audio system active
        this.playsilentAudio();

      } catch (error) {
        // Ignore errors - these are fallback methods
      }
    }, 10000); // Every 10 seconds

    this.logService.info('✅ Keep-alive actions started');
  }

  /**
   * Method 5: Continuous wake lock retry
   */
  private startWakeLockRetry(): void {
    if (this.wakeLockRetryInterval) {
      clearInterval(this.wakeLockRetryInterval);
    }

    this.wakeLockRetryInterval = setInterval(() => {
      if (this.isSupported && (!this.wakeLock || this.wakeLock.released)) {
        this.logService.info('🔄 Wake lock not active - attempting to reacquire');
        this.requestOfficialWakeLock();
      }
    }, 15000); // Check every 15 seconds

    this.logService.info('✅ Wake lock retry mechanism started');
  }

  /**
   * Method 6: Prevent screen saver
   */
  private preventScreenSaver(): void {
    // Disable context menu to prevent accidental actions
    document.addEventListener('contextmenu', (e) => e.preventDefault());

    // Prevent default on common sleep-inducing keys
    document.addEventListener('keydown', (e) => {
      if (e.key === 'F1' || (e.altKey && e.key === 'F4')) {
        e.preventDefault();
      }
    });

    this.logService.info('✅ Screen saver prevention activated');
  }

  /**
   * Method 7: Aggressive visibility change handling
   */
  private setupVisibilityHandling(): void {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        this.logService.info('🔥 Page became visible - reactivating ALL keep-alive methods!');
        setTimeout(() => {
          if (this.isSupported) {
            this.requestOfficialWakeLock();
          }
          // Restart video if needed
          if (this.videoElement && this.videoElement.paused) {
            this.videoElement.play().catch(() => {});
          }
        }, 100);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);
    window.addEventListener('pageshow', handleVisibilityChange);

    this.logService.info('✅ Visibility change handling activated');
  }

  /**
   * Play silent audio to keep audio system active
   */
  private playsilentAudio(): void {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      gainNode.gain.value = 0; // Silent
      oscillator.frequency.value = 440;
      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.01); // Very short
    } catch (error) {
      // Ignore audio errors
    }
  }

  /**
   * Release all wake lock methods
   */
  async releaseWakeLock(): Promise<void> {
    this.logService.info('🔄 Releasing all keep-alive methods');
    this.isActive = false;

    // Clear all intervals
    if (this.keepAliveInterval) {
      clearInterval(this.keepAliveInterval);
      this.keepAliveInterval = null;
    }

    if (this.wakeLockRetryInterval) {
      clearInterval(this.wakeLockRetryInterval);
      this.wakeLockRetryInterval = null;
    }

    if (this.mouseMoveInterval) {
      clearInterval(this.mouseMoveInterval);
      this.mouseMoveInterval = null;
    }

    // Remove video element
    if (this.videoElement) {
      this.videoElement.remove();
      this.videoElement = null;
    }

    // Release official wake lock
    if (this.wakeLock && !this.wakeLock.released) {
      try {
        await this.wakeLock.release();
        this.logService.info('✅ Official wake lock released');
      } catch (error: any) {
        this.logService.error(`❌ Error releasing wake lock: ${error.message}`);
      }
    }

    this.wakeLock = null;
    this.logService.info('✅ All keep-alive methods released');
  }

  /**
   * Get comprehensive wake lock status
   */
  getWakeLockStatus(): { supported: boolean; active: boolean; type?: string; methods: string[] } {
    const activeMethods: string[] = [];
    
    if (this.wakeLock && !this.wakeLock.released) {
      activeMethods.push('Official Wake Lock API');
    }
    
    if (this.videoElement) {
      activeMethods.push('Invisible Video');
    }
    
    if (this.mouseMoveInterval) {
      activeMethods.push('Mouse Movement Simulation');
    }
    
    if (this.keepAliveInterval) {
      activeMethods.push('Keep-Alive Actions');
    }
    
    if (this.wakeLockRetryInterval) {
      activeMethods.push('Wake Lock Retry');
    }

    return {
      supported: this.isSupported,
      active: this.isActive,
      type: this.wakeLock?.type,
      methods: activeMethods
    };
  }

  /**
   * Force reactivate all methods - NUCLEAR OPTION
   */
  async forceReactivate(): Promise<void> {
    this.logService.info('🚨 NUCLEAR OPTION: Force reactivating ALL keep-alive methods!');
    await this.releaseWakeLock();
    setTimeout(() => {
      this.requestWakeLock();
    }, 1000);
  }

  /**
   * Clean up on service destruction
   */
  ngOnDestroy(): void {
    this.releaseWakeLock();
  }
}