import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

import { StartupSyncService, SyncStatus } from './core/services/startup-sync.service';
import { LogService } from './core/services/log.service';
import { SupabaseApiService } from './core/services/supabase-api.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, CommonModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent implements OnInit, OnDestroy {
  
  orientationLabel: string | null = null;
  private orientationTimeout: any = null;
  private orientationOverride: 'portrait' | 'landscape' | null = null;
  title = 'angular-resay-tv-digital-signage';
  syncStatus$: Observable<SyncStatus>;
  showSyncOverlay = false;
  isOnline = typeof window !== 'undefined' ? navigator.onLine : false;

  constructor(
    private startupSync: StartupSyncService,
    private logService: LogService,
    private supabaseApi: SupabaseApiService
  ) {
    this.syncStatus$ = this.startupSync.syncStatus$;
  }

  private orientationListener = () => this.applyOrientationClass();

  async ngOnInit() {
    this.logService.info('Application starting - initializing offline-first sync');
    
    // Listen for online/offline events (browser only)
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => this.isOnline = true);
      window.addEventListener('offline', () => this.isOnline = false);

      // Orientation change handling
      this.applyOrientationClass();
      window.addEventListener('orientationchange', this.orientationListener);
    }
    
    // Subscribe to sync status to show/hide overlay
    this.syncStatus$.subscribe(status => {
      this.showSyncOverlay = status.isInitialSync && !status.isComplete;
    });

    // Initialize the app with startup sync
    try {
      await this.startupSync.initializeApp();
      this.logService.info('Application initialization complete');

      // After initialization, fetch orientation preference from backend
      this.fetchScreenOrientation();
    } catch (error) {
      this.logService.error('Application initialization failed', error);
    }
  }

  ngOnDestroy(): void {
    if (typeof window !== 'undefined') {
      window.removeEventListener('orientationchange', this.orientationListener);
    }
  }

  private applyOrientationClass(): void {
    if (typeof window === 'undefined') return;

    // Start with browser-detected orientation
    let isPortrait = window.matchMedia('(orientation: portrait)').matches;

    // If we have an override from backend, honor it
    if (this.orientationOverride !== null) {
      isPortrait = this.orientationOverride === 'portrait';
    }

    
    const body = document.body;
    if (isPortrait) {
      body.classList.add('portrait-mode');
      this.showOrientation('Portrait');
    } else {
      body.classList.remove('portrait-mode');
      this.showOrientation('Landscape');
    }
  }

  private showOrientation(label: string): void {
    this.orientationLabel = label;
    if (this.orientationTimeout) {
      clearTimeout(this.orientationTimeout);
    }
    this.orientationTimeout = setTimeout(() => {
      this.orientationLabel = null;
    }, 2000);
  }

  /**
   * Fetch orientation configuration for this device from Supabase
   */
  private async fetchScreenOrientation(): Promise<void> {
    if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
      return;
    }

    const deviceId = localStorage.getItem('deviceId');
    if (!deviceId) {
      this.logService.warn('Cannot fetch screen orientation: device ID missing');
      return;
    }

    try {
      const screen = await this.supabaseApi.getScreenById(deviceId).toPromise();
      if (!screen) {
        this.logService.warn('Screen not found for orientation fetch');
        return;
      }

      // Determine orientation preference from screen data
      if (screen.orientation) {
        this.orientationOverride = screen.orientation as 'portrait' | 'landscape';
      } else if (screen.settings && typeof screen.settings.screen_rotation === 'number') {
        const rotation = screen.settings.screen_rotation;
        this.orientationOverride = (rotation === 90 || rotation === 270) ? 'portrait' : 'landscape';
      }

      // Apply updated orientation
      if (this.orientationOverride !== null) {
        this.applyOrientationClass();
      }
    } catch (error) {
      this.logService.error('Failed to fetch screen orientation', error);
    }
  }
}