.portrait-warning-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #111;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  z-index: 10002; /* Higher than other overlays */
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.warning-content {
  max-width: 400px;
  padding: 20px;
}

.warning-icon {
  font-size: 6rem;
  margin-bottom: 20px;
  color: #ffc107;
  animation: rotate-icon 4s linear infinite;
}

.warning-title {
  font-size: 2rem;
  font-weight: 300;
  margin-bottom: 15px;
}

.warning-message {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.6;
}

@keyframes rotate-icon {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(90deg);
  }
  75% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
